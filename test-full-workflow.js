const MKVService = require('./src/services/mkvService');
const TMDBService = require('./src/services/tmdbService');

async function testFullWorkflow() {
    console.log('🧪 Testing Full MKV Tag Manager Workflow...\n');
    
    const mkvService = new MKVService();
    const tmdbService = new TMDBService();
    
    try {
        // Step 1: Load MKV file and parse existing tags
        console.log('1. Loading MKV file and parsing tags...');
        const mkvInfo = await mkvService.getMKVInfo('./test_files/Chicago Fire - S01E01 - Pilot.mkv');
        
        console.log('Original MKV Tags:');
        mkvInfo.tags.forEach((tag, i) => {
            console.log(`\nTag ${i}:`);
            tag.simpleTags.forEach(st => {
                console.log(`  ${st.name}: "${st.value || ''}"`);
            });
        });
        
        // Step 2: Parse filename for TMDB search
        console.log('\n2. Parsing filename for TMDB search...');
        const filename = 'Chicago Fire - S01E01 - Pilot.mkv';
        const parsed = tmdbService.parseFilename(filename);
        console.log('Parsed filename:', parsed);
        
        // Step 3: Search TMDB
        console.log('\n3. Searching TMDB...');
        const searchResults = await tmdbService.searchTVShows(parsed.title);
        console.log(`Found ${searchResults.results.length} results`);
        
        if (searchResults.results.length > 0) {
            const selectedShow = searchResults.results[0];
            console.log(`Selected: ${selectedShow.name}`);
            
            // Step 4: Get detailed show information
            console.log('\n4. Getting detailed show information...');
            const showDetails = await tmdbService.getTVShowDetails(selectedShow.id);
            
            // Step 5: Get episode information
            console.log('\n5. Getting episode information...');
            const episodeDetails = await tmdbService.getEpisodeDetails(selectedShow.id, parsed.season, parsed.episode);
            
            // Step 6: Combine show and episode data
            const combinedData = {
                ...showDetails,
                ...episodeDetails,
                title: episodeDetails.name,
                overview: episodeDetails.overview || showDetails.overview
            };
            
            console.log('\nCombined TMDB Data:');
            console.log(`- Title: ${combinedData.title}`);
            console.log(`- Show: ${combinedData.name}`);
            console.log(`- Overview: ${combinedData.overview?.substring(0, 100)}...`);
            console.log(`- Air Date: ${combinedData.airDate}`);
            console.log(`- Genres: ${combinedData.genres?.map(g => g.name).join(', ')}`);
            console.log(`- Cast: ${combinedData.cast?.slice(0, 3).map(c => c.name).join(', ')}`);
            console.log(`- IMDB: ${combinedData.externalIds?.imdb_id}`);
            console.log(`- Rating: ${combinedData.voteAverage}`);
            
            // Step 7: Convert to MKV tags
            console.log('\n6. Converting TMDB data to MKV tags...');
            const tmdbTags = tmdbService.convertToMKVTags(combinedData);
            
            console.log('TMDB-derived MKV Tags:');
            tmdbTags.forEach((tag, i) => {
                console.log(`\nTMDB Tag ${i}:`);
                tag.simpleTags.forEach(st => {
                    console.log(`  ${st.name}: "${st.value}"`);
                });
            });
            
            // Step 8: Simulate form field mapping
            console.log('\n7. Simulating form field mapping...');
            const fieldMappings = {};
            
            // Map original MKV tags
            mkvInfo.tags.forEach(tag => {
                tag.simpleTags.forEach(st => {
                    if (st.name && !st.name.startsWith('_STATISTICS') && !st.name.startsWith('DURATION')) {
                        fieldMappings[st.name] = {
                            original: st.value || '',
                            tmdb: '',
                            fieldId: `tag-${st.name}`
                        };
                    }
                });
            });
            
            // Map TMDB tags
            tmdbTags.forEach(tag => {
                tag.simpleTags.forEach(st => {
                    if (!fieldMappings[st.name]) {
                        fieldMappings[st.name] = {
                            original: '',
                            tmdb: st.value,
                            fieldId: `tag-${st.name}`
                        };
                    } else {
                        fieldMappings[st.name].tmdb = st.value;
                    }
                });
            });
            
            console.log('\nField Mapping Summary:');
            Object.entries(fieldMappings).forEach(([tagName, data]) => {
                console.log(`${tagName}:`);
                console.log(`  Field ID: ${data.fieldId}`);
                console.log(`  Original: "${data.original}"`);
                console.log(`  TMDB: "${data.tmdb}"`);
                console.log(`  Action: ${data.original ? (data.tmdb ? 'Replace/Merge' : 'Keep Original') : (data.tmdb ? 'Fill from TMDB' : 'Empty')}`);
            });
            
            // Step 9: Test tag writing
            console.log('\n8. Testing tag structure validation...');
            const testTags = [
                {
                    targetTypeValue: 50,
                    simpleTags: Object.entries(fieldMappings)
                        .filter(([_, data]) => data.tmdb || data.original)
                        .map(([tagName, data]) => ({
                            name: tagName,
                            value: data.tmdb || data.original
                        }))
                }
            ];
            
            const isValid = mkvService.validateTagStructure(testTags[0]);
            console.log(`Tag structure validation: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
            
            if (isValid) {
                console.log('\nFinal tag structure for writing:');
                testTags[0].simpleTags.forEach(st => {
                    console.log(`  ${st.name}: "${st.value}"`);
                });
            }
        }
        
        console.log('\n✅ Full workflow test completed successfully!');
        console.log('\n📋 Summary:');
        console.log('- ✅ MKV file parsing works');
        console.log('- ✅ Filename parsing works');
        console.log('- ✅ TMDB search and details work');
        console.log('- ✅ Episode data retrieval works');
        console.log('- ✅ Tag conversion works');
        console.log('- ✅ Field mapping logic works');
        console.log('- ✅ Tag validation works');
        
    } catch (error) {
        console.error('❌ Workflow test failed:', error.message);
        console.error(error.stack);
    }
}

testFullWorkflow();
