const TMDBService = require('./src/services/tmdbService');

async function testTMDBMapping() {
    console.log('🧪 Testing TMDB Data Mapping...\n');
    
    const tmdbService = new TMDBService();
    
    try {
        // Test TV show search and details
        console.log('1. Searching for Chicago Fire...');
        const searchResults = await tmdbService.searchTVShows('Chicago Fire');
        console.log(`Found ${searchResults.results.length} results`);
        
        if (searchResults.results.length > 0) {
            const firstResult = searchResults.results[0];
            console.log(`Selected: ${firstResult.name} (${firstResult.year})`);
            
            // Get detailed information
            console.log('\n2. Getting detailed TV show information...');
            const details = await tmdbService.getTVShowDetails(firstResult.id);
            
            console.log('TV Show Details:');
            console.log(`- Title: ${details.name}`);
            console.log(`- Overview: ${details.overview?.substring(0, 100)}...`);
            console.log(`- First Air Date: ${details.firstAirDate}`);
            console.log(`- Genres: ${details.genres?.map(g => g.name).join(', ')}`);
            console.log(`- Cast: ${details.cast?.slice(0, 3).map(c => c.name).join(', ')}`);
            console.log(`- IMDB ID: ${details.externalIds?.imdb_id}`);
            console.log(`- Rating: ${details.voteAverage}`);
            console.log(`- Poster: ${details.posterPath ? 'Available' : 'Not available'}`);
            
            // Convert to MKV tags
            console.log('\n3. Converting to MKV tags...');
            const mkvTags = tmdbService.convertToMKVTags(details);
            
            console.log('Converted MKV Tags:');
            mkvTags.forEach((tag, i) => {
                console.log(`\nTag ${i} (Target: ${tag.targetTypeValue}):`);
                tag.simpleTags.forEach(st => {
                    console.log(`  ${st.name}: "${st.value}"`);
                });
            });
            
            // Test episode details
            console.log('\n4. Testing episode details...');
            try {
                const seasonDetails = await tmdbService.getSeasonDetails(details.id, 1);
                console.log(`Season 1 has ${seasonDetails.episodes.length} episodes`);
                
                if (seasonDetails.episodes.length > 0) {
                    const episodeDetails = await tmdbService.getEpisodeDetails(details.id, 1, 1);
                    console.log(`Episode 1x01: "${episodeDetails.name}"`);
                    console.log(`Episode Overview: ${episodeDetails.overview?.substring(0, 100)}...`);
                    
                    // Convert episode data
                    const combinedData = {
                        ...details,
                        ...episodeDetails,
                        title: episodeDetails.name,
                        overview: episodeDetails.overview || details.overview
                    };
                    
                    const episodeTags = tmdbService.convertToMKVTags(combinedData);
                    console.log('\nEpisode Tags:');
                    episodeTags.forEach((tag, i) => {
                        console.log(`\nEpisode Tag ${i}:`);
                        tag.simpleTags.forEach(st => {
                            console.log(`  ${st.name}: "${st.value}"`);
                        });
                    });
                }
            } catch (episodeError) {
                console.log('Episode test failed:', episodeError.message);
            }
        }
        
        // Test movie search
        console.log('\n5. Testing movie search...');
        const movieResults = await tmdbService.searchMovies('Ghosted');
        if (movieResults.results.length > 0) {
            const movie = movieResults.results[0];
            console.log(`Found movie: ${movie.title} (${movie.year})`);
            
            const movieDetails = await tmdbService.getMovieDetails(movie.id);
            const movieTags = tmdbService.convertToMKVTags(movieDetails);
            
            console.log('Movie Tags:');
            movieTags.forEach((tag, i) => {
                console.log(`\nMovie Tag ${i}:`);
                tag.simpleTags.forEach(st => {
                    console.log(`  ${st.name}: "${st.value}"`);
                });
            });
        }
        
        console.log('\n✅ TMDB mapping test completed successfully!');
        
    } catch (error) {
        console.error('❌ TMDB mapping test failed:', error.message);
    }
}

testTMDBMapping();
