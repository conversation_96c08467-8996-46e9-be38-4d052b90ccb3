<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https://image.tmdb.org;">
    <title>MKV Tag Manager by WikiZell</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>MKV Tag Manager</h1>
            <p class="subtitle">by WikiZell</p>
        </header>

        <main class="main-content">
            <!-- File Selection Section -->
            <section class="file-section">
                <div class="file-input-group">
                    <button id="selectFileBtn" class="btn btn-primary">Select MKV File</button>
                    <span id="selectedFile" class="file-path">No file selected</span>
                </div>
                <div id="fileInfo" class="file-info hidden">
                    <p><strong>File:</strong> <span id="fileName"></span></p>
                    <p><strong>Size:</strong> <span id="fileSize"></span></p>
                    <p><strong>Modified:</strong> <span id="fileModified"></span></p>
                </div>
            </section>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Left Panel - Tag Editor -->
                <section class="tags-panel">
                    <h2>MKV Tags</h2>
                    <div id="loadingTags" class="loading hidden">Loading tags...</div>

                    <!-- Tag Source Control -->
                    <div id="tagSourceControl" class="tag-source-control hidden">
                        <div class="source-options">
                            <h4>Tag Data Source</h4>
                            <div class="source-buttons">
                                <button id="useOriginalBtn" class="btn btn-secondary source-btn active">Use Original Tags</button>
                                <button id="useTMDBBtn" class="btn btn-primary source-btn" disabled>Use TMDB Data</button>
                                <button id="mergeDataBtn" class="btn btn-success source-btn" disabled>Merge Both</button>
                            </div>
                            <p class="source-description">
                                <span id="sourceDescription">Showing original tags from the MKV file</span>
                            </p>
                        </div>
                    </div>

                    <!-- Tag Template View -->
                    <div id="tagTemplate" class="tag-template hidden">
                        <!-- Media Info Section -->
                        <div class="media-info-section">
                            <div class="media-poster">
                                <img id="mediaPoster" src="" alt="Media Poster" class="poster-image hidden">
                                <div id="posterPlaceholder" class="poster-placeholder">
                                    <span>No Image</span>
                                </div>
                            </div>
                            <div class="media-basic-info">
                                <h3 id="mediaTitle" class="media-title">Media Title</h3>
                                <p id="mediaYear" class="media-year"></p>
                                <p id="mediaType" class="media-type"></p>
                            </div>
                        </div>

                        <!-- Tag Categories -->
                        <div class="tag-categories">
                            <!-- Title Information -->
                            <div class="tag-category">
                                <h4>Title Information</h4>
                                <div class="tag-fields">
                                    <div class="tag-field">
                                        <label for="tag-TITLE">Title/Show Name</label>
                                        <input type="text" id="tag-TITLE" class="tag-input" placeholder="Enter title or show name">
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-SUBTITLE">Subtitle</label>
                                        <input type="text" id="tag-SUBTITLE" class="tag-input" placeholder="Enter subtitle">
                                    </div>
                                </div>
                            </div>

                            <!-- Content Description -->
                            <div class="tag-category">
                                <h4>Content Description</h4>
                                <div class="tag-fields">
                                    <div class="tag-field">
                                        <label for="tag-DESCRIPTION">Description</label>
                                        <textarea id="tag-DESCRIPTION" class="tag-textarea" rows="3" placeholder="Enter description"></textarea>
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-SUMMARY">Summary</label>
                                        <textarea id="tag-SUMMARY" class="tag-textarea" rows="2" placeholder="Enter summary"></textarea>
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-KEYWORDS">Keywords</label>
                                        <input type="text" id="tag-KEYWORDS" class="tag-input" placeholder="Enter keywords (comma separated)">
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-GENRE">Genre</label>
                                        <input type="text" id="tag-GENRE" class="tag-input" placeholder="Enter genre">
                                    </div>
                                </div>
                            </div>

                            <!-- People -->
                            <div class="tag-category">
                                <h4>People</h4>
                                <div class="tag-fields">
                                    <div class="tag-field">
                                        <label for="tag-DIRECTOR">Director</label>
                                        <input type="text" id="tag-DIRECTOR" class="tag-input" placeholder="Enter director">
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-ACTOR">Actors</label>
                                        <input type="text" id="tag-ACTOR" class="tag-input" placeholder="Enter main actors">
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-WRITTEN_BY">Written By</label>
                                        <input type="text" id="tag-WRITTEN_BY" class="tag-input" placeholder="Enter writer(s)">
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-PRODUCER">Producer</label>
                                        <input type="text" id="tag-PRODUCER" class="tag-input" placeholder="Enter producer">
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-ARTIST">Artist</label>
                                        <input type="text" id="tag-ARTIST" class="tag-input" placeholder="Enter artist">
                                    </div>
                                </div>
                            </div>

                            <!-- Dates -->
                            <div class="tag-category">
                                <h4>Dates</h4>
                                <div class="tag-fields">
                                    <div class="tag-field">
                                        <label for="tag-DATE_RELEASED">Release Date</label>
                                        <input type="date" id="tag-DATE_RELEASED" class="tag-input">
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-DATE_RECORDED">Recording Date</label>
                                        <input type="date" id="tag-DATE_RECORDED" class="tag-input">
                                    </div>
                                </div>
                            </div>

                            <!-- Episode/Season Information -->
                            <div class="tag-category">
                                <h4>Episode/Season Information</h4>
                                <div class="tag-fields">
                                    <div class="tag-field">
                                        <label for="tag-SEASON_NUMBER">Season Number</label>
                                        <input type="number" id="tag-SEASON_NUMBER" class="tag-input" min="1" placeholder="1">
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-PART_NUMBER">Episode Number</label>
                                        <input type="number" id="tag-PART_NUMBER" class="tag-input" min="1" placeholder="1">
                                    </div>
                                </div>
                            </div>

                            <!-- Identifiers -->
                            <div class="tag-category">
                                <h4>External Identifiers</h4>
                                <div class="tag-fields">
                                    <div class="tag-field">
                                        <label for="tag-IMDB">IMDb ID</label>
                                        <input type="text" id="tag-IMDB" class="tag-input" placeholder="tt1234567">
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-TMDB">TMDB ID</label>
                                        <input type="text" id="tag-TMDB" class="tag-input" placeholder="movie/12345 or tv/12345">
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-TVDB">TVDB ID</label>
                                        <input type="text" id="tag-TVDB" class="tag-input" placeholder="12345">
                                    </div>
                                </div>
                            </div>

                            <!-- Rating & Comments -->
                            <div class="tag-category">
                                <h4>Rating & Comments</h4>
                                <div class="tag-fields">
                                    <div class="tag-field">
                                        <label for="tag-RATING">Rating (0-5)</label>
                                        <input type="number" id="tag-RATING" class="tag-input" min="0" max="5" step="0.1" placeholder="4.5">
                                    </div>
                                    <div class="tag-field">
                                        <label for="tag-COMMENT">Comment</label>
                                        <textarea id="tag-COMMENT" class="tag-textarea" rows="2" placeholder="Enter comments"></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Technical -->
                            <div class="tag-category">
                                <h4>Technical Information</h4>
                                <div class="tag-fields">
                                    <div class="tag-field">
                                        <label for="tag-ENCODER">Encoder</label>
                                        <input type="text" id="tag-ENCODER" class="tag-input" placeholder="Enter encoder information">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No File Selected Message -->
                    <div id="noFileMessage" class="no-file-message">
                        <p>Select an MKV file to view and edit tags</p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="tags-actions">
                        <button id="refreshTagsBtn" class="btn btn-secondary" disabled>Refresh Tags</button>
                        <button id="saveTagsBtn" class="btn btn-success" disabled>Save Changes</button>
                        <button id="clearTagsBtn" class="btn btn-warning" disabled>Clear All</button>
                    </div>
                </section>

                <!-- Right Panel - TMDB Search -->
                <section class="tmdb-panel">
                    <h2>TMDB Search</h2>
                    <div class="search-section">
                        <div class="search-input-group">
                            <input type="text" id="searchInput" placeholder="Enter movie/TV show name..." class="search-input">
                            <button id="searchBtn" class="btn btn-primary">Search</button>
                        </div>
                        <div class="search-options">
                            <label>
                                <input type="radio" name="searchType" value="movie" checked> Movie
                            </label>
                            <label>
                                <input type="radio" name="searchType" value="tv"> TV Show
                            </label>
                        </div>
                        <button id="parseFilenameBtn" class="btn btn-secondary">Parse from Filename</button>
                    </div>
                    
                    <div id="searchResults" class="search-results">
                        <p class="no-results">Search for content to see results</p>
                    </div>

                    <!-- Episode Selection for TV Shows -->
                    <div id="episodeSelection" class="episode-selection hidden">
                        <h3>Select Episode</h3>
                        <div class="episode-controls">
                            <label>Season: 
                                <select id="seasonSelect" class="form-select">
                                    <option value="">Select Season</option>
                                </select>
                            </label>
                            <label>Episode: 
                                <select id="episodeSelect" class="form-select">
                                    <option value="">Select Episode</option>
                                </select>
                            </label>
                        </div>
                        <button id="applyEpisodeBtn" class="btn btn-success" disabled>Apply Episode Data</button>
                    </div>
                </section>
            </div>
        </main>

        <!-- Status Bar -->
        <footer class="status-bar">
            <span id="statusText">Ready</span>
            <div class="progress-container hidden" id="progressContainer">
                <div class="progress-bar" id="progressBar"></div>
            </div>
        </footer>
    </div>



    <script src="renderer.js"></script>
</body>
</html>
