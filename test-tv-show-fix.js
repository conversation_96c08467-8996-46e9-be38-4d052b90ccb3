const TMDBService = require('./src/services/tmdbService');

async function testTVShowFix() {
    console.log('🧪 Testing TV Show TMDB Data Application Fix...\n');
    
    const tmdbService = new TMDBService();
    
    try {
        // Simulate the exact workflow when clicking on a TV show result
        console.log('1. Searching for Chicago Fire...');
        const searchResults = await tmdbService.searchTVShows('Chicago Fire');
        
        if (searchResults.results.length > 0) {
            const tvShow = searchResults.results[0];
            console.log(`✅ Found: ${tvShow.name} (${tvShow.year})`);
            console.log(`   - ID: ${tvShow.id}`);
            console.log(`   - Type: ${tvShow.type}`);
            
            // Step 2: Get TV show details (this is what happens when you click on the result)
            console.log('\n2. Getting TV show details...');
            const details = await tmdbService.getTVShowDetails(tvShow.id);
            
            console.log('✅ TV Show details loaded:');
            console.log(`   - Name: ${details.name}`);
            console.log(`   - Overview: ${details.overview?.substring(0, 100)}...`);
            console.log(`   - First Air Date: ${details.firstAirDate}`);
            console.log(`   - Genres: ${details.genres?.map(g => g.name).join(', ')}`);
            console.log(`   - Cast: ${details.cast?.slice(0, 3).map(c => c.name).join(', ')}`);
            console.log(`   - IMDB: ${details.externalIds?.imdb_id}`);
            console.log(`   - Rating: ${details.voteAverage}`);
            console.log(`   - Poster: ${details.posterPath ? 'Available' : 'Not available'}`);
            
            // Step 3: Convert to MKV tags (this should happen immediately now)
            console.log('\n3. Converting TV show data to MKV tags...');
            const mkvTags = tmdbService.convertToMKVTags(details);
            
            console.log('✅ TV Show MKV Tags generated:');
            mkvTags.forEach((tag, i) => {
                console.log(`\nTag ${i} (Target: ${tag.targetTypeValue}):`);
                tag.simpleTags.forEach(st => {
                    console.log(`  ${st.name}: "${st.value}"`);
                });
            });
            
            // Step 4: Verify form field mapping
            console.log('\n4. Verifying form field mapping...');
            const expectedFields = [
                'TITLE', 'DESCRIPTION', 'DATE_RELEASED', 'GENRE', 
                'ACTOR', 'TMDB', 'IMDB', 'RATING'
            ];
            
            console.log('Expected form field population:');
            expectedFields.forEach(fieldName => {
                const tagData = mkvTags[0]?.simpleTags.find(st => st.name === fieldName);
                const fieldId = `tag-${fieldName}`;
                const hasValue = tagData && tagData.value;
                
                console.log(`  ${fieldId}: ${hasValue ? `"${tagData.value}"` : 'empty'} ${hasValue ? '✅' : '⚪'}`);
            });
            
            // Step 5: Test episode selection workflow
            console.log('\n5. Testing episode selection workflow...');
            if (details.seasons && details.seasons.length > 0) {
                const season1 = details.seasons.find(s => s.season_number === 1);
                if (season1) {
                    console.log(`✅ Season 1 available: ${season1.episode_count} episodes`);
                    
                    // Get season details
                    const seasonDetails = await tmdbService.getSeasonDetails(details.id, 1);
                    console.log(`✅ Season details loaded: ${seasonDetails.episodes.length} episodes`);
                    
                    if (seasonDetails.episodes.length > 0) {
                        // Get first episode
                        const episodeDetails = await tmdbService.getEpisodeDetails(details.id, 1, 1);
                        console.log(`✅ Episode details loaded: "${episodeDetails.name}"`);
                        
                        // Combine show and episode data
                        const combinedData = {
                            ...details,
                            ...episodeDetails,
                            title: episodeDetails.name,
                            overview: episodeDetails.overview || details.overview
                        };
                        
                        // Convert combined data
                        const episodeTags = tmdbService.convertToMKVTags(combinedData);
                        
                        console.log('\n✅ Episode-specific tags:');
                        episodeTags[0].simpleTags.forEach(st => {
                            console.log(`  ${st.name}: "${st.value}"`);
                        });
                    }
                }
            }
            
            console.log('\n✅ TV Show fix test completed successfully!');
            console.log('\n📋 Summary:');
            console.log('- ✅ TV show search works');
            console.log('- ✅ TV show details load correctly');
            console.log('- ✅ Show-level tags generate properly');
            console.log('- ✅ Form fields should populate immediately');
            console.log('- ✅ Episode selection workflow available');
            console.log('- ✅ Episode-specific data overrides show data');
            
        } else {
            console.log('❌ No search results found');
        }
        
    } catch (error) {
        console.error('❌ TV Show fix test failed:', error.message);
    }
}

testTVShowFix();
