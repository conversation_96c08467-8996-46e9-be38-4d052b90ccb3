const TMDBService = require('./src/services/tmdbService');

async function testTMDBApplication() {
    console.log('🧪 Testing TMDB Data Application to Form Fields...\n');
    
    const tmdbService = new TMDBService();
    
    try {
        // Test 1: Search for Chicago Fire
        console.log('1. Searching for Chicago Fire...');
        const searchResults = await tmdbService.searchTVShows('Chicago Fire');
        
        if (searchResults.results.length > 0) {
            const show = searchResults.results[0];
            console.log(`✅ Found: ${show.name} (${show.year})`);
            
            // Test 2: Get show details
            console.log('\n2. Getting show details...');
            const showDetails = await tmdbService.getTVShowDetails(show.id);
            console.log(`✅ Show details loaded`);
            console.log(`   - Genres: ${showDetails.genres?.map(g => g.name).join(', ')}`);
            console.log(`   - Cast: ${showDetails.cast?.slice(0, 3).map(c => c.name).join(', ')}`);
            console.log(`   - IMDB: ${showDetails.externalIds?.imdb_id}`);
            console.log(`   - Poster: ${showDetails.posterPath ? 'Available' : 'Not available'}`);
            
            // Test 3: Get episode details
            console.log('\n3. Getting episode details (S01E01)...');
            const episodeDetails = await tmdbService.getEpisodeDetails(show.id, 1, 1);
            console.log(`✅ Episode details loaded: "${episodeDetails.name}"`);
            console.log(`   - Air Date: ${episodeDetails.airDate}`);
            console.log(`   - Overview: ${episodeDetails.overview?.substring(0, 100)}...`);
            
            // Test 4: Combine data (as done in the app)
            console.log('\n4. Combining show and episode data...');
            const combinedData = {
                ...showDetails,
                ...episodeDetails,
                title: episodeDetails.name,
                overview: episodeDetails.overview || showDetails.overview
            };
            
            console.log(`✅ Combined data prepared`);
            console.log(`   - Title: ${combinedData.title}`);
            console.log(`   - Show: ${combinedData.name}`);
            console.log(`   - Type: ${combinedData.type}`);
            
            // Test 5: Convert to MKV tags
            console.log('\n5. Converting to MKV tags...');
            const mkvTags = tmdbService.convertToMKVTags(combinedData);
            
            console.log('✅ MKV Tags generated:');
            mkvTags.forEach((tag, i) => {
                console.log(`\nTag ${i} (Target: ${tag.targetTypeValue}):`);
                tag.simpleTags.forEach(st => {
                    console.log(`  ${st.name}: "${st.value}"`);
                });
            });
            
            // Test 6: Simulate form field mapping
            console.log('\n6. Simulating form field mapping...');
            const formFields = [
                'TITLE', 'SUBTITLE', 'DESCRIPTION', 'SUMMARY', 'KEYWORDS', 'GENRE',
                'DIRECTOR', 'ACTOR', 'WRITTEN_BY', 'PRODUCER', 'ARTIST',
                'DATE_RELEASED', 'DATE_RECORDED', 'IMDB', 'TMDB', 'TVDB',
                'RATING', 'COMMENT', 'ENCODER', 'SEASON_NUMBER', 'PART_NUMBER'
            ];
            
            console.log('Form field mapping simulation:');
            formFields.forEach(fieldName => {
                const tagData = mkvTags[0]?.simpleTags.find(st => st.name === fieldName);
                const fieldId = `tag-${fieldName}`;
                const hasValue = tagData && tagData.value;
                
                console.log(`  ${fieldId}: ${hasValue ? `"${tagData.value}"` : 'empty'} ${hasValue ? '✅' : '⚪'}`);
            });
            
            // Test 7: Check specific mappings
            console.log('\n7. Verifying specific TMDB mappings...');
            const expectedMappings = [
                { field: 'TITLE', expected: episodeDetails.name },
                { field: 'DESCRIPTION', expected: episodeDetails.overview || showDetails.overview },
                { field: 'DATE_RELEASED', expected: episodeDetails.airDate },
                { field: 'GENRE', expected: showDetails.genres?.map(g => g.name).join(', ') },
                { field: 'ACTOR', expected: showDetails.cast?.slice(0, 5).map(c => c.name).join(', ') },
                { field: 'TMDB', expected: `${combinedData.type}/${combinedData.id}` },
                { field: 'IMDB', expected: showDetails.externalIds?.imdb_id },
                { field: 'RATING', expected: (showDetails.voteAverage / 2).toFixed(1) }
            ];
            
            expectedMappings.forEach(mapping => {
                const tagData = mkvTags[0]?.simpleTags.find(st => st.name === mapping.field);
                const actualValue = tagData?.value;
                const matches = actualValue === mapping.expected;
                
                console.log(`  ${mapping.field}: ${matches ? '✅' : '❌'} Expected: "${mapping.expected}", Got: "${actualValue}"`);
            });
            
        } else {
            console.log('❌ No search results found');
        }
        
        console.log('\n✅ TMDB application test completed!');
        
    } catch (error) {
        console.error('❌ TMDB application test failed:', error.message);
    }
}

testTMDBApplication();
